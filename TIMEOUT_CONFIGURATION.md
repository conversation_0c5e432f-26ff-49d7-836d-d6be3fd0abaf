# Timeout Configuration for Market Basket Analysis Backend

This document explains the timeout configurations implemented to handle large dataset processing.

## Problem
When processing large CSV files for market basket analysis, the application may encounter timeout errors due to:
- Large file uploads (>10MB)
- Data cleaning operations on large datasets
- Model training with many transactions
- Memory-intensive operations

## Solutions Implemented

### 1. Backend Timeout Settings (Flask)

**File: `marketbasketbackendapp/mbapp.py`**

- **Request Timeout**: 1800 seconds (30 minutes)
- **Session Lifetime**: 1800 seconds (30 minutes)
- **Max Content Length**: 50MB for file uploads
- **Threading**: Enabled for better concurrent request handling

```python
app.config['PERMANENT_SESSION_LIFETIME'] = 1800  # 30 minutes
app.config['REQUEST_TIMEOUT'] = 1800  # 30 minutes for request timeout
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max upload size
```

### 2. Production Server Configuration (Gunicorn)

**File: `gunicorn_config.py`**

- **Worker Timeout**: 1800 seconds (30 minutes)
- **Single Worker**: Prevents memory conflicts with large datasets
- **Memory Management**: Automatic worker restart after 100 requests

```python
timeout = 1800  # 30 minutes
workers = 1  # Single worker for memory efficiency
max_requests = 100  # Restart workers to prevent memory leaks
```

### 3. Frontend Timeout Settings

**File: `marketbasketbackendapp/templates/upload.html`**

- **Request Timeout**: 1800000ms (30 minutes)
- **User Feedback**: Clear messaging about processing time
- **Progress Indicators**: Visual feedback during long operations

```javascript
const timeoutId = setTimeout(() => controller.abort(), 1800000); // 30 minute timeout
```

### 4. Memory Optimization

**Features implemented:**
- Chunked reading for files >50,000 rows
- Memory monitoring with psutil
- Conservative training parameters for large datasets
- Automatic garbage collection

## Usage Instructions

### Development Mode
```bash
# Install dependencies
pip install -r requirements.txt

# Run with extended timeout
python start_server.py --mode dev --install-deps
```

### Production Mode
```bash
# Install dependencies including Gunicorn
pip install -r requirements.txt

# Run with Gunicorn (recommended for production)
python start_server.py --mode prod --install-deps

# Or manually with Gunicorn
gunicorn --config gunicorn_config.py marketbasketbackendapp.mbapp:app
```

### Manual Flask Run
```bash
# Set environment variables
export FLASK_APP=marketbasketbackendapp/mbapp.py
export FLASK_ENV=development

# Run with Python
python marketbasketbackendapp/mbapp.py
```

## Troubleshooting Timeout Issues

### If you still get timeout errors:

1. **Increase timeout values further:**
   - Edit `PROCESSING_TIMEOUT` in `mbapp.py`
   - Update `timeout` in `gunicorn_config.py`
   - Modify frontend timeout in `upload.html`

2. **Reduce dataset size:**
   - Split large CSV files into smaller chunks
   - Remove unnecessary columns before upload
   - Sample your data if full dataset isn't required

3. **Optimize server resources:**
   - Increase available RAM
   - Use SSD storage for faster I/O
   - Close other memory-intensive applications

4. **Use production server:**
   - Gunicorn handles timeouts better than Flask dev server
   - Better memory management
   - More stable for large operations

### Error Messages and Solutions

| Error | Cause | Solution |
|-------|-------|----------|
| "Request Timeout" | Frontend timeout | Increase timeout in upload.html |
| "502 Bad Gateway" | Backend timeout | Increase Gunicorn timeout |
| "Memory Error" | Insufficient RAM | Use smaller dataset or more RAM |
| "413 Request Entity Too Large" | File too large | Increase MAX_CONTENT_LENGTH |

## Configuration Files Summary

- `mbapp.py`: Main Flask application with timeout settings
- `gunicorn_config.py`: Production server configuration
- `start_server.py`: Startup script with dependency management
- `requirements.txt`: Updated with psutil and gunicorn
- `upload.html`: Frontend with extended timeout

## Monitoring

The application includes memory monitoring to help identify resource issues:
- Memory usage before/after data cleaning
- Available memory warnings
- Process memory tracking (requires psutil)

For production deployments, consider adding:
- Application performance monitoring (APM)
- Log aggregation
- Resource monitoring dashboards
