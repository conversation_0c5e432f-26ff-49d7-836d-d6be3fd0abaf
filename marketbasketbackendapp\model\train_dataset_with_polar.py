"""
Market Basket Analysis Training Module

This module provides functions for cleaning transaction data and training
market basket analysis models using efficient_apriori library.

PRODUCTION USAGE:
- Functions are imported and used by the Flask web application (mbapp.py)
- Data comes from user uploads through the web interface
- No file paths needed - data is passed as pandas DataFrames

TESTING USAGE:
- Run this file directly: python train_dataset_with_polar.py
- Update the data_path in the __main__ section to point to your test CSV

Required CSV columns: InvoiceNumber, ProductID, ProductName
"""

# We using polars for efficient data processing, pandas for compatibility
import polars as pl
import pandas as pd

# Basket Libraries
from efficient_apriori import apriori

def clean_data(data):
    """
    Clean the input data for market basket analysis.

    Args:
        data: pandas DataFrame containing the transaction data

    Returns:
        pandas DataFrame with cleaned data
    """
    # Check if required columns exist
    if 'InvoiceNumber' in data.columns:
        data['InvoiceNumber'] = data['InvoiceNumber'].astype(str).str.strip()
    else:
        print("Error: 'InvoiceNumber' column not found in the data.")
        return None

    if 'ProductID' in data.columns:
        data['ProductID'] = data['ProductID'].astype(str).str.strip()
    else:
        print("Error: 'ProductID' column not found in the data.")
        return None

    if 'ProductName' in data.columns:
        data['ProductName'] = data['ProductName'].astype(str).str.strip()
    else:
        print("Error: 'ProductName' column not found in the data.")
        return None

    print("Data info before cleaning:")
    print(data.info())

    # Drop rows with missing values in required columns
    if 'InvoiceNumber' in data.columns and 'ProductID' in data.columns and 'ProductName' in data.columns:
        data = data.dropna(subset=['InvoiceNumber', 'ProductID','ProductName'])
        print("Data info after dropping NaN values:")
        print(data.info())

    # Group by the appropriate columns to remove duplicates
    print(f'Number of unique products before grouping: {data["ProductID"].nunique()}')
    cleaned_data = data.groupby(['InvoiceNumber', 'ProductID','ProductName']).size().reset_index(name='count')
    cleaned_data = cleaned_data[['InvoiceNumber', 'ProductID','ProductName']]
    print(f'Number of unique products after grouping: {cleaned_data["ProductID"].nunique()}')

    # Final cleanup
    cleaned_data['InvoiceNumber'] = cleaned_data['InvoiceNumber'].astype(str).str.strip()
    print("Final cleaned data info:")
    print(cleaned_data.info())

    return cleaned_data


def model_training(df, product_ids=None, memory_optimized=False):
    """
    Train a market basket analysis model using efficient_apriori.

    Args:
        df: pandas DataFrame containing the transaction data
        product_ids: Optional list of product IDs to filter by (not used in this implementation)
        memory_optimized: If True, use more conservative parameters to reduce memory usage

    Returns:
        pandas DataFrame containing association rules
    """
    print(f"Starting model training with memory_optimized={memory_optimized}")

    try:
        # Convert pandas DataFrame to polars for efficient processing
        df_pl = pl.from_pandas(df)

        # For very large datasets, sample the data to reduce memory usage
        if memory_optimized and len(df) > 100000:
            sample_size = min(100000, int(len(df) * 0.5))  # 50% or max 100k rows
            print(f"Memory optimization: Sampling {sample_size} rows from {len(df)} total rows")
            df_pl = df_pl.sample(n=sample_size, seed=42)

        # Group transactions and create transaction lists
        print("Creating transaction format...")
        df_grouped = (df_pl[['InvoiceNumber','ProductID']]
                     .sort(['InvoiceNumber', 'ProductID'])
                     .unique(subset=['InvoiceNumber','ProductID'], maintain_order=True)
                     .group_by('InvoiceNumber').agg([pl.col('ProductID')])
                     )

        transactions = [tuple(row[1]) for row in df_grouped.iter_rows()]
        print(f"Number of transactions: {len(transactions)}")

        # Calculate dynamic parameters based on dataset size
        N = len(transactions)

        # Set parameters based on dataset size and memory optimization
        if memory_optimized:
            if N > 5000:
                min_support = 0.005  # Higher support for memory optimization
                min_confidence = 0.3  # Higher confidence for memory optimization
            elif N > 1000:
                min_support = 0.01
                min_confidence = 0.25
            else:
                min_support = 0.02
                min_confidence = 0.2
        else:
            # Use the original parameters for better results
            if N > 5000:
                    min_support = 0.00025
                    min_confidence = 0.01
            elif N > 1000:
                    min_support = 0.001
                    min_confidence = 0.05
            else:
                    min_support = 0.005
                    min_confidence = 0.1

        print(f"Using min_support: {min_support}, min_confidence: {min_confidence}")

        # Generate frequent itemsets and rules
        print("Generating frequent itemsets and association rules...")
        itemsets, rules = apriori(transactions,
                                min_support=min_support,
                                min_confidence=min_confidence)

        print(f"Number of frequent itemsets: {len(itemsets)}")
        print(f"Number of rules generated: {len(rules)}")

        # Filter rules to ensure both antecedents and consequents exist
        filtered_rules = [rule for rule in rules if len(rule.lhs) >= 1 and len(rule.rhs) >= 1]
        print(f"Number of filtered rules: {len(filtered_rules)}")

        # Sort the filtered rules by confidence (descending)
        sorted_rules = sorted(filtered_rules, key=lambda rule: rule.confidence, reverse=True)

        # Limit rules for memory optimization
        if memory_optimized and len(sorted_rules) > 10000:
            print(f"Memory optimization: Limiting rules from {len(sorted_rules)} to 10000")
            sorted_rules = sorted_rules[:10000]

        # Create a list of dictionaries for the DataFrame
        rules_data = [
            {
                "antecedents": list(rule.lhs),  # Convert lhs to a list
                "consequents": list(rule.rhs),  # Convert rhs to a list
                "lift": rule.lift,
                "confidence": rule.confidence,
                "conviction": rule.conviction,
                "support": rule.support,
            }
            for rule in sorted_rules
        ]

        # Create a pandas DataFrame (not polars) for compatibility with the rest of the system
        rules_df = pd.DataFrame(rules_data)

        print(f"Final number of rules in DataFrame: {len(rules_df)}")
        if len(rules_df) > 0:
            print("Sample rules:")
            print(rules_df.head())

        return rules_df

    except MemoryError:
        print("Memory error occurred during model training")
        raise MemoryError("Insufficient memory to complete model training. Try using a smaller dataset or enable memory optimization.")
    except Exception as e:
        print(f"Error in model training: {str(e)}")
        import traceback
        print(traceback.format_exc())
        raise

if __name__ == "__main__":
    import pandas as pd
    import os

    print("=" * 60)
    print("Market Basket Analysis - Training Module")
    print("=" * 60)
    print()
    print("This module provides functions for cleaning data and training")
    print("market basket analysis models using efficient_apriori.")
    print()
    print("USAGE:")
    print("- In production: Data is provided by user uploads through the web interface")
    print("- For testing: Update the data_path below to point to your CSV file")
    print()
    print("Required CSV columns: InvoiceNumber, ProductID, ProductName")
    print("=" * 60)

    # Create data directory if it doesn't exist
    os.makedirs('data', exist_ok=True)

    # For testing purposes only - update this path to your actual CSV file
    # In production, data comes from user uploads via the web interface
    data_path = 'data/sample_monthlySale.csv'  # Update this path for testing

    if os.path.exists(data_path):
        try:
            print(f"Loading test data from: {data_path}")
            data = pd.read_csv(data_path)
            print(f"Loaded data with {len(data)} rows")

            # Clean the data
            print("\nCleaning data...")
            cleaned_data = clean_data(data)

            if cleaned_data is not None:
                # Save cleaned data
                cleaned_data.to_csv('data/cleaned_monthlySale.csv', index=False)
                print("Saved cleaned data to data/cleaned_monthlySale.csv")

                # Train model
                print("\nTraining model...")
                association_rules = model_training(cleaned_data)

                # Save model
                import pickle
                with open('data/test_trained_model.pkl', 'wb') as file:
                    pickle.dump(association_rules, file)
                print("Saved trained model to data/test_trained_model.pkl")
                print(f"Generated {len(association_rules)} association rules")
            else:
                print("Data cleaning failed")
        except Exception as e:
            print(f"Error: {e}")
    else:
        print(f"Test data file not found: {data_path}")
        print("\nTo test this module:")
        print("1. Place your CSV file in the data/ directory")
        print("2. Update the data_path variable above")
        print("3. Run this script again")
        print("\nNote: In production, data is uploaded through the web interface")




















