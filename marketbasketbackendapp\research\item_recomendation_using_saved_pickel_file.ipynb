{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 8, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 9, "status": "ok", "timestamp": 1746012180944, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "91rJaLpCA0Bu", "outputId": "e129610d-6d5b-4d0b-eacc-71add1e57075"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Trained model saved to trained_model.pkl\n"]}], "source": ["import pickle\n", "rules = pickle.load(open('E:/DASUNI/CAREER PATH/Evision Micro Systems/Vs code projects/Market_Basket_Analysis_Project_fullstack/with pickle new edition full stack/mbbackend/marketbasketbackendapp/data/trained_model.pkl', 'rb'))\n", "\n", "# Convert rules to DataFrame if it's not already\n", "if not isinstance(rules, pd.DataFrame):\n", "    rules = pd.DataFrame(rules, columns=['antecedents', 'consequents', 'support', 'confidence', 'lift'])\n", "\n", "filename = 'trained_model.pkl'\n", "pickle.dump(rules, open(filename, 'wb'))\n", "\n", "print(f\"Trained model saved to {filename}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 215, "status": "ok", "timestamp": 1746012181162, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "UzqOBN4SHaXy"}, "outputs": [], "source": ["\n", "# cleaned_data=pd.read_csv('/content/drive/MyDrive/Colab Notebooks/MARKERT BASKET ANALYSIS/input output_invoice data/cleaned_monthlySale.csv')"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 476}, "executionInfo": {"elapsed": 182, "status": "ok", "timestamp": 1746012181359, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "TV7wDsbRENnB", "outputId": "8fe9ba91-a969-439c-ea58-6c66ff2cba1b"}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>antecedents</th>\n", "      <th>consequents</th>\n", "      <th>antecedent support</th>\n", "      <th>consequent support</th>\n", "      <th>support</th>\n", "      <th>confidence</th>\n", "      <th>lift</th>\n", "      <th>representativity</th>\n", "      <th>leverage</th>\n", "      <th>conviction</th>\n", "      <th>zhangs_metric</th>\n", "      <th>j<PERSON><PERSON></th>\n", "      <th>certainty</th>\n", "      <th><PERSON><PERSON><PERSON><PERSON><PERSON></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>(607068, 607087)</td>\n", "      <td>(607069)</td>\n", "      <td>0.011449</td>\n", "      <td>0.023369</td>\n", "      <td>0.011355</td>\n", "      <td>0.991781</td>\n", "      <td>42.440232</td>\n", "      <td>1.0</td>\n", "      <td>0.011088</td>\n", "      <td>118.823453</td>\n", "      <td>0.987746</td>\n", "      <td>0.483957</td>\n", "      <td>0.991584</td>\n", "      <td>0.738843</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>(607068)</td>\n", "      <td>(607069)</td>\n", "      <td>0.022098</td>\n", "      <td>0.023369</td>\n", "      <td>0.021879</td>\n", "      <td>0.990064</td>\n", "      <td>42.366760</td>\n", "      <td>1.0</td>\n", "      <td>0.021363</td>\n", "      <td>98.290946</td>\n", "      <td>0.998461</td>\n", "      <td>0.927527</td>\n", "      <td>0.989826</td>\n", "      <td>0.963153</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>(607068, 607087, 611565)</td>\n", "      <td>(607069)</td>\n", "      <td>0.003843</td>\n", "      <td>0.023369</td>\n", "      <td>0.003795</td>\n", "      <td>0.987755</td>\n", "      <td>42.267963</td>\n", "      <td>1.0</td>\n", "      <td>0.003706</td>\n", "      <td>79.758208</td>\n", "      <td>0.980108</td>\n", "      <td>0.162090</td>\n", "      <td>0.987462</td>\n", "      <td>0.575086</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>(607068, 607087, 611565, 607119)</td>\n", "      <td>(607069)</td>\n", "      <td>0.003639</td>\n", "      <td>0.023369</td>\n", "      <td>0.003592</td>\n", "      <td>0.987069</td>\n", "      <td>42.238602</td>\n", "      <td>1.0</td>\n", "      <td>0.003507</td>\n", "      <td>75.526140</td>\n", "      <td>0.979890</td>\n", "      <td>0.153382</td>\n", "      <td>0.986760</td>\n", "      <td>0.570380</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>(607068, 611565)</td>\n", "      <td>(607069)</td>\n", "      <td>0.005897</td>\n", "      <td>0.023369</td>\n", "      <td>0.005819</td>\n", "      <td>0.986702</td>\n", "      <td>42.222904</td>\n", "      <td>1.0</td>\n", "      <td>0.005681</td>\n", "      <td>73.442660</td>\n", "      <td>0.982108</td>\n", "      <td>0.248161</td>\n", "      <td>0.986384</td>\n", "      <td>0.617848</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13337</th>\n", "      <td>(600308)</td>\n", "      <td>(604924, 604096)</td>\n", "      <td>0.024247</td>\n", "      <td>0.008908</td>\n", "      <td>0.003639</td>\n", "      <td>0.150065</td>\n", "      <td>16.845289</td>\n", "      <td>1.0</td>\n", "      <td>0.003423</td>\n", "      <td>1.166079</td>\n", "      <td>0.964011</td>\n", "      <td>0.123273</td>\n", "      <td>0.142425</td>\n", "      <td>0.279258</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13338</th>\n", "      <td>(606413)</td>\n", "      <td>(603775, 606402)</td>\n", "      <td>0.028639</td>\n", "      <td>0.008626</td>\n", "      <td>0.004297</td>\n", "      <td>0.150055</td>\n", "      <td>17.395440</td>\n", "      <td>1.0</td>\n", "      <td>0.004050</td>\n", "      <td>1.166397</td>\n", "      <td>0.970302</td>\n", "      <td>0.130352</td>\n", "      <td>0.142659</td>\n", "      <td>0.324118</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13339</th>\n", "      <td>(602562)</td>\n", "      <td>(602386, 600631, 602561)</td>\n", "      <td>0.020389</td>\n", "      <td>0.007811</td>\n", "      <td>0.003058</td>\n", "      <td>0.150000</td>\n", "      <td>19.204819</td>\n", "      <td>1.0</td>\n", "      <td>0.002899</td>\n", "      <td>1.167282</td>\n", "      <td>0.967659</td>\n", "      <td>0.121647</td>\n", "      <td>0.143309</td>\n", "      <td>0.270783</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13340</th>\n", "      <td>(602562)</td>\n", "      <td>(606402, 602388)</td>\n", "      <td>0.020389</td>\n", "      <td>0.009520</td>\n", "      <td>0.003058</td>\n", "      <td>0.150000</td>\n", "      <td>15.756178</td>\n", "      <td>1.0</td>\n", "      <td>0.002864</td>\n", "      <td>1.165270</td>\n", "      <td>0.956025</td>\n", "      <td>0.113902</td>\n", "      <td>0.141830</td>\n", "      <td>0.235626</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13341</th>\n", "      <td>(602562)</td>\n", "      <td>(602948, 605622)</td>\n", "      <td>0.020389</td>\n", "      <td>0.009693</td>\n", "      <td>0.003058</td>\n", "      <td>0.150000</td>\n", "      <td>15.475728</td>\n", "      <td>1.0</td>\n", "      <td>0.002861</td>\n", "      <td>1.165068</td>\n", "      <td>0.954851</td>\n", "      <td>0.113175</td>\n", "      <td>0.141681</td>\n", "      <td>0.232767</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>13342 rows × 14 columns</p>\n", "</div>"], "text/plain": ["                            antecedents               consequents  \\\n", "0                      (607068, 607087)                  (607069)   \n", "1                              (607068)                  (607069)   \n", "2              (607068, 607087, 611565)                  (607069)   \n", "3      (607068, 607087, 611565, 607119)                  (607069)   \n", "4                      (607068, 611565)                  (607069)   \n", "...                                 ...                       ...   \n", "13337                          (600308)          (604924, 604096)   \n", "13338                          (606413)          (603775, 606402)   \n", "13339                          (602562)  (602386, 600631, 602561)   \n", "13340                          (602562)          (606402, 602388)   \n", "13341                          (602562)          (602948, 605622)   \n", "\n", "       antecedent support  consequent support   support  confidence  \\\n", "0                0.011449            0.023369  0.011355    0.991781   \n", "1                0.022098            0.023369  0.021879    0.990064   \n", "2                0.003843            0.023369  0.003795    0.987755   \n", "3                0.003639            0.023369  0.003592    0.987069   \n", "4                0.005897            0.023369  0.005819    0.986702   \n", "...                   ...                 ...       ...         ...   \n", "13337            0.024247            0.008908  0.003639    0.150065   \n", "13338            0.028639            0.008626  0.004297    0.150055   \n", "13339            0.020389            0.007811  0.003058    0.150000   \n", "13340            0.020389            0.009520  0.003058    0.150000   \n", "13341            0.020389            0.009693  0.003058    0.150000   \n", "\n", "            lift  representativity  leverage  conviction  zhangs_metric  \\\n", "0      42.440232               1.0  0.011088  118.823453       0.987746   \n", "1      42.366760               1.0  0.021363   98.290946       0.998461   \n", "2      42.267963               1.0  0.003706   79.758208       0.980108   \n", "3      42.238602               1.0  0.003507   75.526140       0.979890   \n", "4      42.222904               1.0  0.005681   73.442660       0.982108   \n", "...          ...               ...       ...         ...            ...   \n", "13337  16.845289               1.0  0.003423    1.166079       0.964011   \n", "13338  17.395440               1.0  0.004050    1.166397       0.970302   \n", "13339  19.204819               1.0  0.002899    1.167282       0.967659   \n", "13340  15.756178               1.0  0.002864    1.165270       0.956025   \n", "13341  15.475728               1.0  0.002861    1.165068       0.954851   \n", "\n", "        j<PERSON><PERSON>  <PERSON>  \n", "0      0.483957   0.991584    0.738843  \n", "1      0.927527   0.989826    0.963153  \n", "2      0.162090   0.987462    0.575086  \n", "3      0.153382   0.986760    0.570380  \n", "4      0.248161   0.986384    0.617848  \n", "...         ...        ...         ...  \n", "13337  0.123273   0.142425    0.279258  \n", "13338  0.130352   0.142659    0.324118  \n", "13339  0.121647   0.143309    0.270783  \n", "13340  0.113902   0.141830    0.235626  \n", "13341  0.113175   0.141681    0.232767  \n", "\n", "[13342 rows x 14 columns]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["rules"]}, {"cell_type": "code", "execution_count": 12, "metadata": {"executionInfo": {"elapsed": 14, "status": "ok", "timestamp": 1746012181403, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "iKAhuW9UhQSb"}, "outputs": [], "source": ["def recommend_items(items, rules, top_n=200):\n", "    # Convert input items to a set\n", "    item_set = set(items)\n", "\n", "    # Filter rules for exact matches\n", "    exact_match_rules = rules[rules['antecedents'].apply(lambda x: set(x) == item_set)]\n", "    print(f\"Number of exact match rules: {len(exact_match_rules)}\")\n", "\n", "    # Filter rules for single-item antecedents that overlap with input items\n", "    single_item_rules = rules[rules['antecedents'].apply(lambda x: any(item in x for item in items))]\n", "    print(f\"Number of single item rules: {len(single_item_rules)}\")\n", "\n", "\n", "    # Combine exact and single-item rules\n", "    combined_rules = pd.concat([exact_match_rules, single_item_rules]).drop_duplicates()\n", "    print(f\"Number of combined rules: {len(combined_rules)}\")\n", "\n", "    # Filter for single-consequent rules\n", "    single_consequent_rules = combined_rules[combined_rules['consequents'].apply(lambda x: len(x) == 1)]\n", "    print(f\"Number of single-consequent rules: {len(single_consequent_rules)}\")\n", "\n", "    if not single_consequent_rules.empty:\n", "        # Sort by confidence and lift\n", "        sorted_rules = single_consequent_rules.sort_values(by=['confidence', 'lift'], ascending=[False, False])\n", "        recommendations = sorted_rules.head(top_n)\n", "        return recommendations\n", "    else:\n", "        print(\"No single-consequent rules found, considering multi-item consequents.\")\n", "\n", "        # Fall back to multi-consequent rules\n", "        multi_consequent_rules = combined_rules[combined_rules['consequents'].apply(lambda x: len(x) > 1)]\n", "        print(f\"Number of multi-consequent rules: {len(multi_consequent_rules)}\")\n", "\n", "        if multi_consequent_rules.empty:\n", "            print(\"No recommendations found.\")\n", "            return pd.DataFrame()\n", "\n", "        # Sort and return top multi-consequent recommendations\n", "        sorted_multi_rules = multi_consequent_rules.sort_values(by=['confidence', 'lift'], ascending=[False, False])\n", "        recommendations = sorted_multi_rules.head(top_n)\n", "        return recommendations\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {"executionInfo": {"elapsed": 3, "status": "ok", "timestamp": 1746012181409, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "irRMetGW2yR9"}, "outputs": [], "source": ["def output(data, recommendations, item_to_check):\n", "\n", "    if 'ProductID' not in data.columns:\n", "        print(\"Error: 'ProductID' column missing in data.\")\n", "        return [], []\n", "\n", "    # Check if ProductName column exists; fallback to ProductID if missing\n", "    if 'ProductName' in data.columns:\n", "        data_unique = data[['ProductID', 'ProductName']].drop_duplicates()\n", "        product_id_to_name = dict(zip(data_unique['ProductID'].astype(str), data_unique['ProductName']))\n", "    else:\n", "        print(\"Warning: 'ProductName' column not found in data. Using ProductID instead.\")\n", "        data_unique = data[['ProductID']].drop_duplicates()\n", "        product_id_to_name = dict(zip(data_unique['ProductID'].astype(str), data_unique['ProductID']))\n", "\n", "    # Display the recommendations\n", "    print(f\"Top unique items to recommend for '{item_to_check}':\")\n", "    if recommendations.empty:\n", "        print(\"No recommendations found.\")\n", "        return [], []\n", "\n", "    print(recommendations[['antecedents', 'consequents', 'support', 'confidence', 'lift']])\n", "\n", "    # Gather product names for the given item_to_check\n", "    given_item_names = []\n", "    for item in item_to_check:\n", "        given_item_name = product_id_to_name.get(str(item), \"Product Name Not Found\")\n", "        print(f\"For given Item '{item}': {given_item_name}\")\n", "        given_item_names.append(given_item_name)\n", "\n", "    # Gather recommended product names\n", "    product_ids = set()\n", "    for consequents in recommendations['consequents']:\n", "        if isinstance(consequents, (frozenset, set, list)):\n", "            product_ids.update(map(str, consequents))  # Convert to string for matching\n", "        else:\n", "            product_ids.add(str(consequents))  # Ensure single items are strings\n", "\n", "    output = []\n", "    for product_id in product_ids:\n", "        product_name = product_id_to_name.get(product_id, \"Product Name Not Found\")\n", "        print(f\"Product ID: {product_id}, Product Name: {product_name}\")\n", "        output.append((product_id, product_name))\n", "\n", "    return given_item_names, output"]}, {"cell_type": "code", "execution_count": 14, "metadata": {"executionInfo": {"elapsed": 4, "status": "ok", "timestamp": 1746012181416, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "d889Tdlh23e8"}, "outputs": [], "source": ["def Model_run(data, item_to_check):\n", "    assoc_rules = rules\n", "    # Generate recommendations\n", "    recommendations = recommend_items(item_to_check, assoc_rules)\n", "    if recommendations is None or recommendations.empty:\n", "        print(\"No recommendations found.\")\n", "        return\n", "    else:\n", "        print('F1 SUCCESS')\n", "\n", "    # Generate the final output\n", "    final_output = output(data, recommendations,item_to_check)  # Ensure that item_to_check is passed if needed\n", "    if final_output is None or len(final_output) == 0:\n", "        print(\"No final output generated.\")\n", "        return\n", "    else:\n", "        print(final_output)\n", "        print('F2 SUCCESS')\n", "        return final_output"]}], "metadata": {"accelerator": "TPU", "colab": {"authorship_tag": "ABX9TyNxLCYFpFuSpPpTEzIKhdEq", "gpuType": "V28", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 0}