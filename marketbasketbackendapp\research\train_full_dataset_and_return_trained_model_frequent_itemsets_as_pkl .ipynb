{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2505, "status": "ok", "timestamp": 1745996391825, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "YxHhYWT--qdA", "outputId": "f1ea4d55-d863-4a0d-c3fe-a9c17e9e8a35"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: mlxtend in /usr/local/lib/python3.11/dist-packages (0.23.4)\n", "Requirement already satisfied: scipy>=1.2.1 in /usr/local/lib/python3.11/dist-packages (from mlxtend) (1.15.2)\n", "Requirement already satisfied: numpy>=1.16.2 in /usr/local/lib/python3.11/dist-packages (from mlxtend) (2.0.2)\n", "Requirement already satisfied: pandas>=0.24.2 in /usr/local/lib/python3.11/dist-packages (from mlxtend) (2.2.2)\n", "Requirement already satisfied: scikit-learn>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from mlxtend) (1.6.1)\n", "Requirement already satisfied: matplotlib>=3.0.0 in /usr/local/lib/python3.11/dist-packages (from mlxtend) (3.10.0)\n", "Requirement already satisfied: joblib>=0.13.2 in /usr/local/lib/python3.11/dist-packages (from mlxtend) (1.4.2)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.0.0->mlxtend) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.0.0->mlxtend) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.0.0->mlxtend) (4.57.0)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.0.0->mlxtend) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.0.0->mlxtend) (24.2)\n", "Requirement already satisfied: pillow>=8 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.0.0->mlxtend) (11.2.1)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.0.0->mlxtend) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.0.0->mlxtend) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=0.24.2->mlxtend) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=0.24.2->mlxtend) (2025.2)\n", "Requirement already satisfied: threadpoolctl>=3.1.0 in /usr/local/lib/python3.11/dist-packages (from scikit-learn>=1.3.1->mlxtend) (3.6.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.7->matplotlib>=3.0.0->mlxtend) (1.17.0)\n"]}], "source": ["# !pip install mlxtend\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"executionInfo": {"elapsed": 288, "status": "ok", "timestamp": 1745996392132, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "6O8dZJNwK324"}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 359, "status": "aborted", "timestamp": 1745996513856, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "sSgUqrcAaF7w"}, "outputs": [], "source": ["# data=pd.read_csv('/content/drive/MyDrive/Colab Notebooks/MARKERT BASKET ANALYSIS/input output_invoice data/monthlySale.csv')"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 404, "status": "aborted", "timestamp": 1745996513908, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "WvlasIxvaFoN"}, "outputs": [], "source": ["def clean_data(data):\n", "    if 'InvoiceNumber' in data.columns:\n", "        data['InvoiceNumber'] = data['InvoiceNumber']#if column names have simple adjestments\n", "        data['InvoiceNumber'] = data['InvoiceNumber'].astype(str).str.strip()\n", "    else:\n", "        print(\"Error: 'InvoiceNumber' column not found in the data.\")\n", "        return None\n", "    if 'ProductID' in data.columns:\n", "        data['ProductID'] = data['ProductID'].astype(str).str.strip()\n", "    else:\n", "        print(\"Error: 'ProductID' column not found in the data.\")\n", "        return None\n", "    if 'ProductName' in data.columns:\n", "        data['ProductName'] = data['ProductName'].astype(str).str.strip()\n", "    else:\n", "        print(\"Error: 'ProductName' column not found in the data.\")\n", "        return None\n", "    data.info()\n", "    # handle missing values\n", "    # remove raws that contain misssing values\n", "    if 'InvoiceNumber' in data.columns and 'ProductID' in data.columns and 'ProductName' in data.columns:\n", "        data = data.dropna(subset=['InvoiceNumber', 'ProductID','ProductName'])\n", "        data.info()\n", "    # Group by the appropriate columns\n", "    print(f'number of products{data[\"ProductID\"].nunique()}')\n", "    cleaned_data= data.groupby(['InvoiceNumber', 'ProductID','ProductName']).sum().reset_index()\n", "    cleaned_data=cleaned_data[['InvoiceNumber', 'ProductID','ProductName']]\n", "    print(f'number of products{cleaned_data[\"ProductID\"].nunique()}')\n", "    cleaned_data['InvoiceNumber'] = cleaned_data['InvoiceNumber'].astype(str).str.strip()\n", "    cleaned_data.info()\n", "    return cleaned_data"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 45, "status": "aborted", "timestamp": 1745996513955, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "sbII6HU8190-"}, "outputs": [], "source": ["def calculate_min_support(N):\n", "    if N > 5000:\n", "        return 0.0025  # lower than before\n", "    elif 2000 < N <= 5000:\n", "        return 0.003\n", "    elif 1000 < N <= 2000:\n", "        return 0.01\n", "    else:\n", "        return 0.02\n"]}, {"cell_type": "markdown", "metadata": {"id": "GGSZ6A4lY9hj"}, "source": ["lowering it slightly to pick up more itemsets\n"]}, {"cell_type": "markdown", "metadata": {"id": "IJx646ZpaC_R"}, "source": ["Lower thresholds allow more rules to be included, even if they're less strong (lift-wise).\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 124875, "status": "aborted", "timestamp": 1745996513967, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "J6JFDLe_cETj"}, "outputs": [], "source": ["def calculate_min_threshold(N):\n", "    if N > 1000:\n", "        return 0.18  # lower to get more rules\n", "    elif 500 < N <= 1000:\n", "        return 0.2\n", "    else:\n", "        return 0.25"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 124875, "status": "aborted", "timestamp": 1745996513969, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "B7LWsKwnZ9Ax"}, "outputs": [], "source": ["# def calculate_min_threshold(N):\n", "#       if N > 1000:\n", "#         threshold = 0.2 # Higher threshold for very large datasets\n", "#       elif 500 < N <= 1000:\n", "#           threshold = 0.25\n", "#       else:\n", "#           threshold = 0.3\n", "#       return threshold\n", "#       print(f\"Calculated minimum threshold: {threshold} for dataset size: {N}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {"executionInfo": {"elapsed": 124879, "status": "aborted", "timestamp": 1745996513976, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "YWkSX1zJTOzo"}, "outputs": [], "source": ["def model_training(flat_data, product_ids=None):\n", "\n", "    # Pivot the data to create a basket format\n", "    basket = (\n", "        flat_data\n", "        .groupby(['InvoiceNumber', 'ProductID'])['ProductID']\n", "        .count().unstack()\n", "        .reset_index().fillna(0)\n", "        .set_index(\"InvoiceNumber\")\n", "    )\n", "\n", "    # Function to hot encode the values\n", "    def encode_values(x):\n", "        return 1 if x > 0 else 0\n", "\n", "\n", "    # Hot encode the basket\n", "    basket_encoded = basket.applymap(encode_values)\n", "\n", "    # Filter transactions containing at least 2 items\n", "    basket_filtered = basket_encoded[(basket_encoded > 0).sum(axis=1) >= 2]\n", "    # Calculate the number of transactions after filtering\n", "    N = len(basket_filtered)\n", "    print(f\"Number of transactions after filtering: {N}\")\n", "\n", "\n", "    # Dynamically set min_support and min_threshold\n", "    min_support = calculate_min_support(N)\n", "    min_threshold = calculate_min_threshold(N)\n", "    print(f\"Calculated min_support: {min_support}, min_threshold: {min_threshold}\")\n", "\n", "    # Generate frequent itemsets\n", "    frequent_itemsets = apriori(\n", "        basket_filtered, min_support=min_support, use_colnames=True\n", "    )#.sort_values(\"confidence\", ascending=False)\n", "    print(f\"Number of frequent itemsets: {len(frequent_itemsets)}\")\n", "    print(frequent_itemsets.head())\n", "    print(f\"num freq item sets{len(frequent_itemsets)}\")\n", "\n", "    # # Apply association rules\n", "    # assoc_rules = association_rules(\n", "    #     frequent_itemsets, metric=\"lift\", min_threshold=min_threshold, num_itemsets=1\n", "    # ).sort_values(['confidence', 'lift'], ascending=[False, False]).reset_index(drop=True)\n", "    # Apply association rules\n", "    assoc_rules = association_rules(\n", "        frequent_itemsets, metric=\"confidence\", min_threshold=min_threshold, num_itemsets=1\n", "    ).sort_values(['confidence', 'lift'], ascending=[False, False]).reset_index(drop=True)\n", "    print(f\"Number of rules generated: {len(assoc_rules)}\")\n", "    print(assoc_rules)\n", "    return assoc_rules"]}, {"cell_type": "markdown", "metadata": {"id": "JYCZbPcEa6YV"}, "source": ["Support tells you how often a product or set of products appears in the dataset.\n", "\n", "Confidence tells you how often Y is bought when X is bought.\n", "\n", "* Lift > 1 ⇒ X and Y are positively correlated\n", "* Lift = 1 ⇒ X and Y are independent\n", "* Lift < 1 ⇒ X and Y are negatively correlated"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"background_save": true}, "executionInfo": {"elapsed": 124880, "status": "aborted", "timestamp": 1745996513979, "user": {"displayName": "<PERSON>uni bandara", "userId": "11474750550071789397"}, "user_tz": -330}, "id": "i5B4VampXmEi"}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<ipython-input-17-203b663d0135>:18: FutureWarning: DataFrame.applymap has been deprecated. Use DataFrame.map instead.\n", "  basket_encoded = basket.applymap(encode_values)\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[0;32m<ipython-input-18-cca8350d6592>\u001b[0m in \u001b[0;36m<cell line: 0>\u001b[0;34m()\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[0;32mfrom\u001b[0m \u001b[0mmlxtend\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfrequent_patterns\u001b[0m \u001b[0;32mimport\u001b[0m \u001b[0mapriori\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0massociation_rules\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m      2\u001b[0m \u001b[0;31m# flat_data_extracted = clean_data(data)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m----> 3\u001b[0;31m \u001b[0massociation_rules\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mmodel_training\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mcleaned_data\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m", "\u001b[0;32m<ipython-input-17-203b663d0135>\u001b[0m in \u001b[0;36mmodel_training\u001b[0;34m(flat_data, product_ids)\u001b[0m\n\u001b[1;32m     16\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     17\u001b[0m     \u001b[0;31m# Hot encode the basket\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m---> 18\u001b[0;31m     \u001b[0mbasket_encoded\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mbasket\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapplymap\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mencode_values\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m     19\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m     20\u001b[0m     \u001b[0;31m# Filter transactions containing at least 2 items\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pandas/core/frame.py\u001b[0m in \u001b[0;36mapplymap\u001b[0;34m(self, func, na_action, **kwargs)\u001b[0m\n\u001b[1;32m  10520\u001b[0m             \u001b[0mstacklevel\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mfind_stack_level\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m  10521\u001b[0m         )\n\u001b[0;32m> 10522\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmap\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfunc\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mna_action\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mna_action\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m  10523\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m  10524\u001b[0m     \u001b[0;31m# ----------------------------------------------------------------------\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pandas/core/frame.py\u001b[0m in \u001b[0;36mmap\u001b[0;34m(self, func, na_action, **kwargs)\u001b[0m\n\u001b[1;32m  10466\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mx\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_map_values\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfunc\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mna_action\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mna_action\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m  10467\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m> 10468\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minfer\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__finalize__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m\"map\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m  10469\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m  10470\u001b[0m     def applymap(\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pandas/core/frame.py\u001b[0m in \u001b[0;36mapply\u001b[0;34m(self, func, axis, raw, result_type, args, by_row, engine, engine_kwargs, **kwargs)\u001b[0m\n\u001b[1;32m  10372\u001b[0m             \u001b[0mkwargs\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m,\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m  10373\u001b[0m         )\n\u001b[0;32m> 10374\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mop\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__finalize__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmethod\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;34m\"apply\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m  10375\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m  10376\u001b[0m     def map(\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pandas/core/apply.py\u001b[0m in \u001b[0;36mapply\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    914\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply_raw\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mengine\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mengine\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mengine_kwargs\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mengine_kwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    915\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 916\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply_standard\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    917\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    918\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0magg\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pandas/core/apply.py\u001b[0m in \u001b[0;36mapply_standard\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1061\u001b[0m     \u001b[0;32mdef\u001b[0m \u001b[0mapply_standard\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1062\u001b[0m         \u001b[0;32mif\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mengine\u001b[0m \u001b[0;34m==\u001b[0m \u001b[0;34m\"python\"\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1063\u001b[0;31m             \u001b[0mresults\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mres_index\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply_series_generator\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1064\u001b[0m         \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1065\u001b[0m             \u001b[0mresults\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mres_index\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply_series_numba\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pandas/core/apply.py\u001b[0m in \u001b[0;36mapply_series_generator\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m   1079\u001b[0m             \u001b[0;32mfor\u001b[0m \u001b[0mi\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mv\u001b[0m \u001b[0;32min\u001b[0m \u001b[0menumerate\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mseries_gen\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1080\u001b[0m                 \u001b[0;31m# ignore SettingWithCopy here in case the user mutates\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1081\u001b[0;31m                 \u001b[0mresults\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mfunc\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mv\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m*\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0margs\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m**\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mkwargs\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1082\u001b[0m                 \u001b[0;32mif\u001b[0m \u001b[0misinstance\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mresults\u001b[0m\u001b[0;34m[\u001b[0m\u001b[0mi\u001b[0m\u001b[0;34m]\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mABCSeries\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1083\u001b[0m                     \u001b[0;31m# If we have a view on v, we need to make a copy because\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pandas/core/frame.py\u001b[0m in \u001b[0;36minfer\u001b[0;34m(x)\u001b[0m\n\u001b[1;32m  10464\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m  10465\u001b[0m         \u001b[0;32mdef\u001b[0m \u001b[0minfer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mx\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m> 10466\u001b[0;31m             \u001b[0;32mreturn\u001b[0m \u001b[0mx\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m_map_values\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mfunc\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mna_action\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mna_action\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m  10467\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m  10468\u001b[0m         \u001b[0;32mreturn\u001b[0m \u001b[0mself\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mapply\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0minfer\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0m__finalize__\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mself\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0;34m\"map\"\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pandas/core/base.py\u001b[0m in \u001b[0;36m_map_values\u001b[0;34m(self, mapper, na_action, convert)\u001b[0m\n\u001b[1;32m    919\u001b[0m             \u001b[0;32mreturn\u001b[0m \u001b[0marr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmap\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mmapper\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mna_action\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mna_action\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    920\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m--> 921\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0malgorithms\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmap_array\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0marr\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmapper\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mna_action\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mna_action\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mconvert\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mconvert\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m    922\u001b[0m \u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m    923\u001b[0m     \u001b[0;34m@\u001b[0m\u001b[0mfinal\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n", "\u001b[0;32m/usr/local/lib/python3.11/dist-packages/pandas/core/algorithms.py\u001b[0m in \u001b[0;36mmap_array\u001b[0;34m(arr, mapper, na_action, convert)\u001b[0m\n\u001b[1;32m   1741\u001b[0m     \u001b[0mvalues\u001b[0m \u001b[0;34m=\u001b[0m \u001b[0marr\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mastype\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mobject\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mcopy\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0;32mFalse\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1742\u001b[0m     \u001b[0;32mif\u001b[0m \u001b[0mna_action\u001b[0m \u001b[0;32mis\u001b[0m \u001b[0;32mNone\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0;32m-> 1743\u001b[0;31m         \u001b[0;32mreturn\u001b[0m \u001b[0mlib\u001b[0m\u001b[0;34m.\u001b[0m\u001b[0mmap_infer\u001b[0m\u001b[0;34m(\u001b[0m\u001b[0mvalues\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mmapper\u001b[0m\u001b[0;34m,\u001b[0m \u001b[0mconvert\u001b[0m\u001b[0;34m=\u001b[0m\u001b[0mconvert\u001b[0m\u001b[0;34m)\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[0m\u001b[1;32m   1744\u001b[0m     \u001b[0;32melse\u001b[0m\u001b[0;34m:\u001b[0m\u001b[0;34m\u001b[0m\u001b[0;34m\u001b[0m\u001b[0m\n\u001b[1;32m   1745\u001b[0m         return lib.map_infer_mask(\n", "\u001b[0;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["\n", "# from mlxtend.frequent_patterns import apriori, association_rules\n", "\n", "# association_rules = model_training(cleaned_data)"]}], "metadata": {"accelerator": "GPU", "colab": {"authorship_tag": "ABX9TyN+MT4ct7M1FUViEPcgU43S", "gpuType": "A100", "machine_shape": "hm", "name": "", "provenance": [{"file_id": "1m6446uQBLOSg8l5jb4-iEtqg3A3vxVe4", "timestamp": 1734231599361}], "version": ""}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}