# Gunicorn configuration file for handling large file uploads and long processing times

# Server socket
bind = "0.0.0.0:5000"
backlog = 2048

# Worker processes
workers = 1  # Use single worker to avoid memory issues with large datasets
worker_class = "sync"
worker_connections = 1000
timeout = 1800  # 30 minutes - increased timeout for long-running operations
keepalive = 2

# Restart workers after this many requests, to help prevent memory leaks
max_requests = 100
max_requests_jitter = 10

# Logging
accesslog = "-"
errorlog = "-"
loglevel = "info"
access_log_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(D)s'

# Process naming
proc_name = 'market_basket_backend'

# Server mechanics
daemon = False
pidfile = '/tmp/gunicorn.pid'
user = None
group = None
tmp_upload_dir = None

# SSL (if needed)
# keyfile = None
# certfile = None

# Memory and performance
preload_app = True  # Load application code before forking worker processes
