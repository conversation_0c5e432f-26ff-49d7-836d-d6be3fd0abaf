#!/usr/bin/env python3
"""
Startup script for Market Basket Analysis Backend with extended timeout settings
"""

import os
import sys
import subprocess

def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("Dependencies installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"Error installing dependencies: {e}")
        return False
    return True

def start_development_server():
    """Start the development server with extended timeout"""
    print("Starting development server...")
    os.environ['FLASK_APP'] = 'marketbasketbackendapp/mbapp.py'
    os.environ['FLASK_ENV'] = 'development'
    os.environ['FLASK_DEBUG'] = '1'
    
    # Import and run the app
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    from marketbasketbackendapp.mbapp import app
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True,
        use_reloader=False  # Disable reloader to prevent timeout issues
    )

def start_production_server():
    """Start the production server using Gunicorn with extended timeout"""
    print("Starting production server with Gunicorn...")
    cmd = [
        "gunicorn",
        "--config", "gunicorn_config.py",
        "marketbasketbackendapp.mbapp:app"
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Error starting Gunicorn server: {e}")
        return False
    except FileNotFoundError:
        print("Gunicorn not found. Please install it with: pip install gunicorn")
        return False
    return True

def main():
    """Main function to start the server"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Start Market Basket Analysis Backend')
    parser.add_argument('--mode', choices=['dev', 'prod'], default='dev',
                       help='Server mode: dev (development) or prod (production)')
    parser.add_argument('--install-deps', action='store_true',
                       help='Install dependencies before starting')
    
    args = parser.parse_args()
    
    if args.install_deps:
        if not install_dependencies():
            sys.exit(1)
    
    if args.mode == 'prod':
        start_production_server()
    else:
        start_development_server()

if __name__ == '__main__':
    main()
